
function fun(val, widget_name) {
	var result;
	var text;
	var code = parseInt(val, 10); 	// 10����
	var code_bit;

	// ��ͬ������ͬ�߼�
	if (widget_name == "������") { // ��־����������
		var fault = (code & 0xFF00) >> 8; 	// ������
		var event = (code & 0x00FF);
		// 
		if (event != 1 && event != 2) return "-"; // �¼���(��Ϊ1��2ʱ,��������Ч)
		//
		if (fault == 0) return "-";
		//
		code_bit = fault - 1;	// ������ ת ����bitλ ת����ϵ
	}
	else // �豸��Ϣ�� ����bitλ����
	{
		code_bit = code;
	}

	// ����bitλ ���Ľ���
	if (code_bit == 0) text = "-系统初始化超时";
	else if (code_bit == 1)   text = " " + "外部存储器故障";
	else if (code_bit == 2)   text = " " + "3.3V参考源故障";
	else if (code_bit == 3)   text = " " + "12V电源欠压";
	else if (code_bit == 4)   text = " " + "12V电源过压";
	else if (code_bit == 5)   text = " " + "5V电源欠压";
	else if (code_bit == 6)   text = " " + "5V电源过压";
	else if (code_bit == 7)   text = " " + "24V电源欠压";
	else if (code_bit == 8)   text = " " + "24V电源过压";
	else if (code_bit == 9)   text = " " + "DO输出1短路";
	else if (code_bit == 10)  text = " " + "DO输出2短路";
	else if (code_bit == 11)  text = " " + "DO输出3短路";
	else if (code_bit == 12)  text = " " + "485通信故障";
	else if (code_bit == 13)  text = " " + "CAN通信故障";
	else if (code_bit == 14)  text = " " + "F_HD输出异常";
	else if (code_bit == 15)  text = " " + "MCU内部看门狗";
	else if (code_bit == 16)  text = " " + "整流器过温保护";
	else if (code_bit == 17)  text = " " + "输出母线电容过温保护";
	else if (code_bit == 18)  text = " " + "LLC1&2模块过温保护";
	else if (code_bit == 19)  text = " " + "LLC2&3模块过温保护";
	else if (code_bit == 20)  text = " " + "LLC整流器过温保护";
	else if (code_bit == 21)  text = " " + "LLC电压传感过温保护";
	else if (code_bit == 22)  text = " " + "LLC电流1&2传感器过温保护";
	else if (code_bit == 23)  text = " " + "LLC电流2&3过温保护";
	else if (code_bit == 24)  text = " " + "输出电压传感器故障";
	else if (code_bit == 25)  text = " " + "输出电流传感器故障";
	else if (code_bit == 26)  text = " " + "输入电流传感器故障";
	else if (code_bit == 27)  text = " " + "BUCK模块2&3过温保护";
	else if (code_bit == 28)  text = " " + "输入电容过温保护";
	else if (code_bit == 29)  text = " " + "整流器过温停机";
	else if (code_bit == 30)  text = " " + "输出母线电容过温停机";
	else if (code_bit == 31)  text = " " + "LLC1&2模块过温停机";
	else if (code_bit == 32)  text = " " + "LLC2&3模块过温停机";
	else if (code_bit == 33)  text = " " + "LLC整流器过温停机";
	else if (code_bit == 34)  text = " " + "LLC电压传感过温停机";
	else if (code_bit == 35)  text = " " + "LLC电流1&2传感器过温停机";
	else if (code_bit == 36)  text = " " + "LLC电流2&3过温停机";
	else if (code_bit == 37)  text = " " + "中间母线电容过温停机";
	else if (code_bit == 38)  text = " " + "BUCK整流过温停机";
	else if (code_bit == 39)  text = " " + "BUCK模块1&2过温停机";
	else if (code_bit == 40)  text = " " + "BUCK模块2&3过温停机";
	else if (code_bit == 41)  text = " " + "输入电容过温停机";
	else if (code_bit == 42)  text = " " + "整流器温度传感故障";
	else if (code_bit == 43)  text = " " + "输出母线电容温度传感故障";
	else if (code_bit == 44)  text = " " + "LLC1&2模块温度传感故障";
	else if (code_bit == 45)  text = " " + "LLC2&3模块温度传感故障";
	else if (code_bit == 46)  text = " " + "LLC整流器温度传感故障";
	else if (code_bit == 47)  text = " " + "LLC电压传温度传感故障";
	else if (code_bit == 48)  text = " " + "LLC电流1&2传感器温度传感故障";
	else if (code_bit == 49)  text = " " + "LLC电流2&3温度传感故障";
	else if (code_bit == 50)  text = " " + "LLC上电压传感器故障";
	else if (code_bit == 51)  text = " " + "LLC中电压传感器故障";
	else if (code_bit == 52)  text = " " + "LLC下电压传感器故障";
	else if (code_bit == 53)  text = " " + "BUCK模块2&3温度传感故障";
	else if (code_bit == 54)  text = " " + "输入电容温度传感故障";
	else if (code_bit == 55)  text = " " + "LLC_IPM故障";
	else if (code_bit == 56)  text = " " + "OCV_UPV故障";
	else if (code_bit == 57)  text = " " + "OCV_MIV故障";
	else if (code_bit == 58)  text = " " + "OCV_DNV故障";
	else if (code_bit == 59)  text = " " + "OCP_OUT故障";
	else if (code_bit == 60)  text = " " + "OCP_MII故障";
	else if (code_bit == 61)  text = " " + "OCP_DNI故障";
	else if (code_bit == 62)  text = " " + "OVP_P_VBout故障";
	else if (code_bit == 63)  text = " " + "BUCK_OCP故障";
	else if (code_bit == 64)  text = " " + "BUCK_IPM故障";
	else if (code_bit == 65)  text = " " + "直流输入欠压";
	else if (code_bit == 66)  text = " " + "直流输入过压";
	else if (code_bit == 67)  text = " " + "中间电压欠压";
	else if (code_bit == 68)  text = " " + "中间电压过压";
	else if (code_bit == 69)  text = " " + "输出直流过流";
	else if (code_bit == 70)  text = " " + "输入过流停机";
	else if (code_bit == 71)  text = " " + "输出过流停机";
	else if (code_bit == 72)  text = " " + "控制电源故障";
	else if (code_bit == 73)  text = " " + "散热风扇电源故障";
	else if (code_bit == 74)  text = " " + "预充电路故障";
	else if (code_bit == 75)  text = " " + "绝缘电阻故障";
	else if (code_bit == 76)  text = " " + "温湿度传感器故障";
	else if (code_bit == 77)  text = " " + "环境温湿度过高";
	else if (code_bit == 78)  text = " " + "中间电压传感器短路";
	else if (code_bit == 79)  text = " " + "DC/DC输出故障";
	else if (code_bit == 80)  text = " " + "POW电源信号故障";
	else if (code_bit == 81)  text = " " + "输入欠压";
	else if (code_bit == 82)  text = " " + "输入过压";
	else if (code_bit == 83)  text = " " + "输出母线短路";
	else if (code_bit == 84)  text = " " + "输出过流";
	else if (code_bit == 85)  text = " " + "输出源接触器故障";
	else if (code_bit == 86)  text = " " + "3.3V电源欠压";
	else if (code_bit == 87)  text = " " + "3.3V电源过压";
	else if (code_bit == 88)  text = " " + "输出过压";
	else if (code_bit == 89)  text = " " + "输出功率110%";
	else if (code_bit == 90)  text = " " + "输出功率120%";
	else if (code_bit == 91)  text = " " + "输出功率130%";
	else if (code_bit == 92)  text = " " + "输出功率140%";
	else if (code_bit == 93)  text = " " + "输出功率150%";
	else if (code_bit == 94)  text = " " + "输出功率160%";
	else if (code_bit == 95)  text = " " + "输出功率170%";
	else if (code_bit == 96)  text = " " + "输出功率180%";
	else if (code_bit == 97)  text = " " + "输出功率190%";

	else text = " " + "-未定义";

	return "{ \"Value\": \"" + val + "\", \"Chinese\": \"" + text + "\", \"RGB\": \"" + "" + "\"}";
}
