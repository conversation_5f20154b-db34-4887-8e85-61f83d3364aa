
function fun(val, widget_name) {
	var result;
	var text;
	var code = parseInt(val, 10); 	// 10����
	var code_bit;

	// ��ͬ������ͬ�߼�
	if (widget_name == "������") { // ��־����������
		var fault = (code & 0xFF00) >> 8; 	// ������
		var event = (code & 0x00FF);
		// 
		if (event != 1 && event != 2) return "-"; // �¼���(��Ϊ1��2ʱ,��������Ч)
		//
		if (fault == 0) return "-";
		//
		code_bit = fault - 1;	// ������ ת ����bitλ ת����ϵ
	}
	else // �豸��Ϣ�� ����bitλ����
	{
		code_bit = code;
	}

	// ����bitλ ���Ľ���
	if (code_bit == 0) text = "-ϵͳ��ʼ����ʱ";
	else if (code_bit == 1)   text = " " + "�ⲿ�洢������";
	else if (code_bit == 2)   text = " " + "3.3V�ο�Դ����";
	else if (code_bit == 3)   text = " " + "12V��Դ��ѹ";
	else if (code_bit == 4)   text = " " + "12V��ԴǷѹ";
	else if (code_bit == 5)   text = " " + "5V��Դ��ѹ";
	else if (code_bit == 6)   text = " " + "5V��ԴǷѹ";
	else if (code_bit == 7)   text = " " + "24V��Դ��ѹ";
	else if (code_bit == 8)   text = " " + "24V��ԴǷѹ";
	else if (code_bit == 9)   text = " " + "DO���1��·";
	else if (code_bit == 10)  text = " " + "DO���2��·";
	else if (code_bit == 11)  text = " " + "DO���3��·";
	else if (code_bit == 12)  text = " " + "485ͨ�Ź���";
	else if (code_bit == 13)  text = " " + "CANͨ�Ź���";
	else if (code_bit == 14)  text = " " + "F_HD����ɲ��";
	else if (code_bit == 15)  text = " " + "MCU����������";
	else if (code_bit == 16)  text = " " + "г����ݹ��½���";
	else if (code_bit == 17)  text = " " + "����ĸ�ߵ��ݹ��½���";
	else if (code_bit == 18)  text = " " + "LLC1&2ģ����½���";
	else if (code_bit == 19)  text = " " + "LLC2&3ģ����½���";
	else if (code_bit == 20)  text = " " + "LLCг���й��½���";
	else if (code_bit == 21)  text = " " + "LLC��ѹ�����½���";
	else if (code_bit == 22)  text = " " + "LLC����1&2�����ܹ��½���";
	else if (code_bit == 23)  text = " " + "LLC����2&3���½���";
	else if (code_bit == 24)  text = " " + "�����ѹ����������";
	else if (code_bit == 25)  text = " " + "�����������������";
	else if (code_bit == 26)  text = " " + "�����������������";
	else if (code_bit == 27)  text = " " + "BUCKģ��2&3���½���";
	else if (code_bit == 28)  text = " " + "������ݹ��½���";
	else if (code_bit == 29)  text = " " + "г����ݹ���ͣ��";
	else if (code_bit == 30)  text = " " + "����ĸ�ߵ��ݹ���ͣ��";
	else if (code_bit == 31)  text = " " + "LLC1&2ģ�����ͣ��";
	else if (code_bit == 32)  text = " " + "LLC2&3ģ�����ͣ��";
	else if (code_bit == 33)  text = " " + "LLCг���й���ͣ��";
	else if (code_bit == 34)  text = " " + "LLC��ѹ������ͣ��";
	else if (code_bit == 35)  text = " " + "LLC����1&2�����ܹ���ͣ��";
	else if (code_bit == 36)  text = " " + "LLC����2&3����ͣ��";
	else if (code_bit == 37)  text = " " + "�м�ĸ�ߵ��ݹ���ͣ��";
	else if (code_bit == 38)  text = " " + "BUCK��й���ͣ��";
	else if (code_bit == 39)  text = " " + "BUCKģ��1&2����ͣ��";
	else if (code_bit == 40)  text = " " + "BUCKģ��2&3����ͣ��";
	else if (code_bit == 41)  text = " " + "������ݹ���ͣ��";
	else if (code_bit == 42)  text = " " + "г������¸й���";
	else if (code_bit == 43)  text = " " + "����ĸ�ߵ����¸й���";
	else if (code_bit == 44)  text = " " + "LLC1&2ģ���¸й���";
	else if (code_bit == 45)  text = " " + "LLC2&3ģ���¸й���";
	else if (code_bit == 46)  text = " " + "LLCг�����¸й���";
	else if (code_bit == 47)  text = " " + "LLC��ѹ���¸й���";
	else if (code_bit == 48)  text = " " + "LLC����1&2�������¸й���";
	else if (code_bit == 49)  text = " " + "LLC����2&3�¸й���";
	else if (code_bit == 50)  text = " " + "LLC�ϵ�ѹ����������";
	else if (code_bit == 51)  text = " " + "LLC�е�ѹ����������";
	else if (code_bit == 52)  text = " " + "LLC�µ�ѹ����������";
	else if (code_bit == 53)  text = " " + "BUCKģ��2&3�¸й���";
	else if (code_bit == 54)  text = " " + "��������¸й���";
	else if (code_bit == 55)  text = " " + "LLC_IPM����";
	else if (code_bit == 56)  text = " " + "OCV_UPV����";
	else if (code_bit == 57)  text = " " + "OCV_MIV����";
	else if (code_bit == 58)  text = " " + "OCV_DNV����";
	else if (code_bit == 59)  text = " " + "OCP_OUT����";
	else if (code_bit == 60)  text = " " + "OCP_MII����";
	else if (code_bit == 61)  text = " " + "OCP_DNI����";
	else if (code_bit == 62)  text = " " + "OVP_P_VBout����";
	else if (code_bit == 63)  text = " " + "BUCK_OCP����";
	else if (code_bit == 64)  text = " " + "BUCK_IPM����";
	else if (code_bit == 65)  text = " " + "ֱ�������ѹ";
	else if (code_bit == 66)  text = " " + "ֱ������Ƿѹ";
	else if (code_bit == 67)  text = " " + "�м��ѹ��ѹ";
	else if (code_bit == 68)  text = " " + "�м��ѹǷѹ";
	else if (code_bit == 69)  text = " " + "����ֱ������";
	else if (code_bit == 70)  text = " " + "�������ͣ��";
	else if (code_bit == 71)  text = " " + "�������ͣ��";
	else if (code_bit == 72)  text = " " + "���Ƶ�Դ����";
	else if (code_bit == 73)  text = " " + "ɢ�ȷ��ȵ�Դ����";
	else if (code_bit == 74)  text = " " + "Ԥ����·����";
	else if (code_bit == 75)  text = " " + "���̵�������";
	else if (code_bit == 76)  text = " " + "��ʪ�ȴ���������";
	else if (code_bit == 77)  text = " " + "������ʪ�ȸ�";
	else if (code_bit == 78)  text = " " + "�м��ѹ��������·";
	else if (code_bit == 79)  text = " " + "DC/DC�������";
	else if (code_bit == 80)  text = " " + "POW��Դ�źŹ���";
	else if (code_bit == 81)  text = " " + "�����ѹ";
	else if (code_bit == 82)  text = " " + "���Ƿѹ";
	else if (code_bit == 83)  text = " " + "����ĸ�߶�·";
	else if (code_bit == 84)  text = " " + "�������";
	else if (code_bit == 85)  text = " " + "����Դ�Ӵ�����";
	else if (code_bit == 86)  text = " " + "3.3V��Դ��ѹ";
	else if (code_bit == 87)  text = " " + "3.3V��ԴǷѹ";
	else if (code_bit == 88)  text = " " + "������ѹ";
	else if (code_bit == 89)  text = " " + "�������110%";
	else if (code_bit == 90)  text = " " + "�������120%";
	else if (code_bit == 91)  text = " " + "�������130%";
	else if (code_bit == 92)  text = " " + "�������140%";
	else if (code_bit == 93)  text = " " + "�������150%";
	else if (code_bit == 94)  text = " " + "�������160%";
	else if (code_bit == 95)  text = " " + "�������170%";
	else if (code_bit == 96)  text = " " + "�������180%";
	else if (code_bit == 97)  text = " " + "�������190%";
	else text = " " + "-δ����";

	return "{ \"Value\": \"" + val + "\", \"Chinese\": \"" + text + "\", \"RGB\": \"" + "" + "\"}";
}
