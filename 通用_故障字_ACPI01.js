function fun(val, widget_name) {
	var result;
	var text;
	var code = parseInt(val, 10); 	// 10进制
	var code_bit;

	// 不同参数不同逻辑
	if (widget_name == "故障码") { // 标志类型故障码
		var fault = (code & 0xFF00) >> 8; 	// 故障码
		var event = (code & 0x00FF);
		// 
		if (event != 1 && event != 2) return "-"; // 事件码(不为1或2时,故障码无效)
		//
		if (fault == 0) return "-";
		//
		code_bit = fault - 1;	// 故障码 转 对应bit位 转换关系
	}
	else // 设备信息类 直接bit位操作
	{
		code_bit = code;
	}

	// 根据bit位 输出结果
	if (code_bit == 0) text = "-系统初始化超时";
	else if (code_bit == 1)   text = " " + "外部存储器故障";
	else if (code_bit == 2)   text = " " + "3.3V参考源故障";
	else if (code_bit == 3)   text = " " + "12V电源欠压";
	else if (code_bit == 4)   text = " " + "12V电源过压";
	else if (code_bit == 5)   text = " " + "5V电源欠压";
	else if (code_bit == 6)   text = " " + "5V电源过压";
	else if (code_bit == 7)   text = " " + "24V电源欠压";
	else if (code_bit == 8)   text = " " + "24V电源过压";
	else if (code_bit == 9)   text = " " + "DO输出1短路";
	else if (code_bit == 10)  text = " " + "DO输出2短路";
	else if (code_bit == 11)  text = " " + "DO输出3短路";
	else if (code_bit == 12)  text = " " + "485通信故障";
	else if (code_bit == 13)  text = " " + "CAN通信故障";
	else if (code_bit == 14)  text = " " + "F_HD硬件失败";
	else if (code_bit == 15)  text = " " + "MCU看门狗复位";
	else if (code_bit == 16)  text = " " + "母线电流过流保护";
	else if (code_bit == 17)  text = " " + "输出电容电流过流保护";
	else if (code_bit == 18)  text = " " + "LLC1&2模块过流保护";
	else if (code_bit == 19)  text = " " + "LLC2&3模块过流保护";
	else if (code_bit == 20)  text = " " + "LLC母线谐振过流保护";
	else if (code_bit == 21)  text = " " + "LLC电压采样过流保护";
	else if (code_bit == 22)  text = " " + "LLC输出1&2电感电流过流保护";
	else if (code_bit == 23)  text = " " + "LLC输出2&3过流保护";
	else if (code_bit == 24)  text = " " + "输出电压采样电路故障";
	else if (code_bit == 25)  text = " " + "输出电流采样电路故障";
	else if (code_bit == 26)  text = " " + "输出电流采样电路故障";
	else if (code_bit == 27)  text = " " + "BUCK模块2&3过流保护";
	else if (code_bit == 28)  text = " " + "输入电流过流保护";
	else if (code_bit == 29)  text = " " + "母线电流过流停机";
	else if (code_bit == 30)  text = " " + "输出电容电流过流停机";
	else if (code_bit == 31)  text = " " + "LLC1&2模块过流停机";
	else if (code_bit == 32)  text = " " + "LLC2&3模块过流停机";
	else if (code_bit == 33)  text = " " + "LLC母线谐振过流停机";
	else if (code_bit == 34)  text = " " + "LLC电压采样过流停机";
	else if (code_bit == 35)  text = " " + "LLC输出1&2电感电流过流停机";
	else if (code_bit == 36)  text = " " + "LLC输出2&3过流停机";
	else if (code_bit == 37)  text = " " + "中间电容电流过流停机";
	else if (code_bit == 38)  text = " " + "BUCK谐振过流停机";
	else if (code_bit == 39)  text = " " + "BUCK模块1&2过流停机";
	else if (code_bit == 40)  text = " " + "BUCK模块2&3过流停机";
	else if (code_bit == 41)  text = " " + "输入电流过流停机";
	else if (code_bit == 42)  text = " " + "母线温度过温故障";
	else if (code_bit == 43)  text = " " + "输出电容温度过温故障";
	else if (code_bit == 44)  text = " " + "LLC1&2模块过温故障";
	else if (code_bit == 45)  text = " " + "LLC2&3模块过温故障";
	else if (code_bit == 46)  text = " " + "LLC母线谐振过温故障";
	else if (code_bit == 47)  text = " " + "LLC电压过温故障";
	else if (code_bit == 48)  text = " " + "LLC输出1&2电感过温故障";
	else if (code_bit == 49)  text = " " + "LLC输出2&3过温故障";
	else if (code_bit == 50)  text = " " + "LLC上电压采样电路故障";
	else if (code_bit == 51)  text = " " + "LLC中电压采样电路故障";
	else if (code_bit == 52)  text = " " + "LLC下电压采样电路故障";
	else if (code_bit == 53)  text = " " + "BUCK模块2&3过温故障";
	else if (code_bit == 54)  text = " " + "输入过温故障";
	else if (code_bit == 55)  text = " " + "LLC_IPM故障";
	else if (code_bit == 56)  text = " " + "OCV_UPV故障";
	else if (code_bit == 57)  text = " " + "OCV_MIV故障";
	else if (code_bit == 58)  text = " " + "OCV_DNV故障";
	else if (code_bit == 59)  text = " " + "OCP_OUT故障";
	else if (code_bit == 60)  text = " " + "OCP_MII故障";
	else if (code_bit == 61)  text = " " + "OCP_DNI故障";
	else if (code_bit == 62)  text = " " + "OVP_P_VBout故障";
	else if (code_bit == 63)  text = " " + "BUCK_OCP故障";
	else if (code_bit == 64)  text = " " + "BUCK_IPM故障";
	else if (code_bit == 65)  text = " " + "直流输入欠压";
	else if (code_bit == 66)  text = " " + "直流输入过压";
	else if (code_bit == 67)  text = " " + "中间电压欠压";
	else if (code_bit == 68)  text = " " + "中间电压过压";
	else if (code_bit == 69)  text = " " + "输出直流过流";
	else if (code_bit == 70)  text = " " + "风扇过流停机";
	else if (code_bit == 71)  text = " " + "风扇过流停机";
	else if (code_bit == 72)  text = " " + "控制电源故障";
	else if (code_bit == 73)  text = " " + "散热风扇电源故障";
	else if (code_bit == 74)  text = " " + "预充电路故障";
	else if (code_bit == 75)  text = " " + "绝缘电阻过低";
	else if (code_bit == 76)  text = " " + "温湿度传感器故障";
	else if (code_bit == 77)  text = " " + "机柜内湿度高";
	else if (code_bit == 78)  text = " " + "中间电压采样电路";
	else if (code_bit == 79)  text = " " + "DC/DC过流保护";
	else if (code_bit == 80)  text = " " + "POW电源信号故障";
	else if (code_bit == 81)  text = " " + "输出欠压";
	else if (code_bit == 82)  text = " " + "输出过压";
	else if (code_bit == 83)  text = " " + "输出电容短路";
	else if (code_bit == 84)  text = " " + "风扇故障";
	else if (code_bit == 85)  text = " " + "辅助源连接错误";
	else if (code_bit == 86)  text = " " + "3.3V电源欠压";
	else if (code_bit == 87)  text = " " + "3.3V电源过压";
	else if (code_bit == 88)  text = " " + "输出过压";
	else if (code_bit == 89)  text = " " + "输出过流110%";
	else if (code_bit == 90)  text = " " + "输出过流120%";
	else if (code_bit == 91)  text = " " + "输出过流130%";
	else if (code_bit == 92)  text = " " + "输出过流140%";
	else if (code_bit == 93)  text = " " + "输出过流150%";
	else if (code_bit == 94)  text = " " + "输出过流160%";
	else if (code_bit == 95)  text = " " + "输出过流170%";
	else if (code_bit == 96)  text = " " + "输出过流180%";
	else if (code_bit == 97)  text = " " + "输出过流190%";
	else text = " " + "-未定义";

	return "{ \"Value\": \"" + val + "\", \"Chinese\": \"" + text + "\", \"RGB\": \"" + "" + "\"}";
}
